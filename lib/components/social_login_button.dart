import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:somayya_academy/app/constants/string_constants.dart';

class SocialLoginButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? loginType;
  const SocialLoginButton({super.key, this.onPressed, this.loginType});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: SvgPicture.asset(_getSocialIcon(loginType), width: 30),
    );
  }

  String _getSocialIcon(String? loginType) {
    switch (loginType?.toLowerCase()) {
      case 'google':
        return StringConstants.googleIcon;
      case 'facebook':
        return StringConstants.facebookIcon;
      case 'microsoft':
        return StringConstants.microsoftIcon;
      case 'apple':
        return StringConstants.appleIcon;
      default:
        return StringConstants.facebookIcon;
    }
  }
}

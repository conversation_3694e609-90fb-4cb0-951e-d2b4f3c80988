import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AppText<PERSON>ield extends StatelessWidget {
  final TextEditingController? controller;
  final String labelText;
  final bool obscureText;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;

  const AppTextField({
    super.key,
    this.controller,
    required this.labelText,
    this.obscureText = false,
    this.keyboardType,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    final textObscure = obscureText.obs;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Obx(
        () => TextForm<PERSON>ield(
          controller: controller,
          obscureText: textObscure.value,
          keyboardType: keyboardType,
          decoration: InputDecoration(
            labelText: labelText,
            suffixIcon: obscureText
                ? IconButton(
                    onPressed: () {
                      textObscure.value = !textObscure.value;
                    },
                    icon: Icon(
                      textObscure.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                  )
                : null,
          ),
          validator: validator,
          autovalidateMode: AutovalidateMode.onUserInteraction,
        ),
      ),
    );
  }
}

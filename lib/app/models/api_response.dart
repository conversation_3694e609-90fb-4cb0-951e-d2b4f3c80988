/// Generic API response wrapper that standardizes all API responses
class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final ApiError? error;
  final Map<String, dynamic>? metadata;

  const ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.error,
    this.metadata,
  });

  /// Factory constructor for successful responses
  factory ApiResponse.success({
    T? data,
    String? message,
    Map<String, dynamic>? metadata,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      metadata: metadata,
    );
  }

  /// Factory constructor for error responses
  factory ApiResponse.error({
    required ApiError error,
    String? message,
  }) {
    return ApiResponse<T>(
      success: false,
      error: error,
      message: message,
    );
  }

  /// Factory constructor from JSON
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'],
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : json['data'],
      error: json['error'] != null ? ApiError.fromJson(json['error']) : null,
      metadata: json['metadata'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
      'error': error?.toJson(),
      'metadata': metadata,
    };
  }

  /// Check if response has data
  bool get hasData => data != null;

  /// Check if response has error
  bool get hasError => error != null;

  @override
  String toString() {
    return 'ApiResponse(success: $success, message: $message, data: $data, error: $error)';
  }
}

/// Standardized API error model
class ApiError {
  final String code;
  final String message;
  final Map<String, dynamic>? details;
  final List<ValidationError>? validationErrors;

  const ApiError({
    required this.code,
    required this.message,
    this.details,
    this.validationErrors,
  });

  /// Factory constructor from JSON
  factory ApiError.fromJson(Map<String, dynamic> json) {
    return ApiError(
      code: json['code'] ?? 'UNKNOWN_ERROR',
      message: json['message'] ?? 'An unknown error occurred',
      details: json['details'],
      validationErrors: json['validationErrors'] != null
          ? (json['validationErrors'] as List)
              .map((e) => ValidationError.fromJson(e))
              .toList()
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'details': details,
      'validationErrors': validationErrors?.map((e) => e.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'ApiError(code: $code, message: $message, details: $details)';
  }
}

/// Validation error model for form validation errors
class ValidationError {
  final String field;
  final String message;
  final dynamic rejectedValue;

  const ValidationError({
    required this.field,
    required this.message,
    this.rejectedValue,
  });

  /// Factory constructor from JSON
  factory ValidationError.fromJson(Map<String, dynamic> json) {
    return ValidationError(
      field: json['field'] ?? '',
      message: json['message'] ?? '',
      rejectedValue: json['rejectedValue'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'field': field,
      'message': message,
      'rejectedValue': rejectedValue,
    };
  }

  @override
  String toString() {
    return 'ValidationError(field: $field, message: $message, rejectedValue: $rejectedValue)';
  }
}

/// Paginated response model for list endpoints
class PaginatedResponse<T> {
  final List<T> items;
  final int totalItems;
  final int totalPages;
  final int currentPage;
  final int pageSize;
  final bool hasNext;
  final bool hasPrevious;

  const PaginatedResponse({
    required this.items,
    required this.totalItems,
    required this.totalPages,
    required this.currentPage,
    required this.pageSize,
    required this.hasNext,
    required this.hasPrevious,
  });

  /// Factory constructor from JSON
  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    return PaginatedResponse<T>(
      items: (json['items'] as List? ?? [])
          .map((item) => fromJsonT(item))
          .toList(),
      totalItems: json['totalItems'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      currentPage: json['currentPage'] ?? 1,
      pageSize: json['pageSize'] ?? 10,
      hasNext: json['hasNext'] ?? false,
      hasPrevious: json['hasPrevious'] ?? false,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'items': items,
      'totalItems': totalItems,
      'totalPages': totalPages,
      'currentPage': currentPage,
      'pageSize': pageSize,
      'hasNext': hasNext,
      'hasPrevious': hasPrevious,
    };
  }

  /// Check if there are items
  bool get hasItems => items.isNotEmpty;

  /// Check if this is the first page
  bool get isFirstPage => currentPage == 1;

  /// Check if this is the last page
  bool get isLastPage => currentPage == totalPages;

  @override
  String toString() {
    return 'PaginatedResponse(items: ${items.length}, totalItems: $totalItems, currentPage: $currentPage)';
  }
}

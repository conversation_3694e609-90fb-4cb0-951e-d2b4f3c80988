import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart' hide Response;

import '../../config/app_config.dart';
import '../../services/api_service.dart';
import '../constants/api_constants.dart';

/// Authentication interceptor that handles token attachment and refresh
class AuthInterceptor extends Interceptor {
  final ApiService _apiService = Get.find<ApiService>();

  // Track if we're currently refreshing token to avoid infinite loops
  bool _isRefreshing = false;
  final List<RequestOptions> _pendingRequests = [];

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      // Skip token attachment for auth endpoints that don't need it
      if (_shouldSkipAuth(options.path)) {
        handler.next(options);
        return;
      }

      // Get current token
      final token = await _apiService.getToken();
      if (token != null && token.isNotEmpty) {
        options.headers['Authorization'] = 'Bearer $token';
      }

      handler.next(options);
    } catch (e) {
      if (kDebugMode) {
        print('[AuthInterceptor] Error in onRequest: $e');
      }
      handler.next(options);
    }
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Handle 401 Unauthorized errors
    if (err.response?.statusCode == ApiConstants.statusUnauthorized) {
      try {
        // If we're already refreshing, queue this request
        if (_isRefreshing) {
          _pendingRequests.add(err.requestOptions);
          return;
        }

        // Skip refresh for auth endpoints
        if (_shouldSkipAuth(err.requestOptions.path)) {
          handler.next(err);
          return;
        }

        _isRefreshing = true;

        // Try to refresh token
        final refreshSuccess = await _refreshToken();

        if (refreshSuccess) {
          // Retry the original request
          final response = await _retryRequest(err.requestOptions);
          handler.resolve(response);

          // Process any pending requests
          await _processPendingRequests();
        } else {
          // Refresh failed, clear tokens and redirect to login
          await _handleAuthFailure();
          handler.next(err);
        }
      } catch (e) {
        if (kDebugMode) {
          print('[AuthInterceptor] Error handling 401: $e');
        }
        await _handleAuthFailure();
        handler.next(err);
      } finally {
        _isRefreshing = false;
        _pendingRequests.clear();
      }
    } else {
      handler.next(err);
    }
  }

  /// Check if the endpoint should skip authentication
  bool _shouldSkipAuth(String path) {
    final authEndpoints = [
      ApiConstants.signin,
      ApiConstants.signup,
      ApiConstants.resetPassword,
      ApiConstants.emailVerification,
      '/auth/refresh-token',
    ];

    return authEndpoints.any((endpoint) => path.contains(endpoint));
  }

  /// Attempt to refresh the authentication token
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _apiService.getRefreshToken();
      if (refreshToken == null || refreshToken.isEmpty) {
        return false;
      }

      // Create a new Dio instance to avoid interceptor loops
      final dio = Dio();
      dio.options.baseUrl = AppConfig.apiBaseUrl;
      dio.options.headers.addAll(ApiConstants.defaultHeaders);

      final response = await dio.post(
        '/auth/refresh-token',
        data: {'refreshToken': refreshToken},
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final newToken = data['token'] as String?;
        final newRefreshToken = data['refreshToken'] as String?;

        if (newToken != null) {
          await _apiService.setToken(newToken);
          if (newRefreshToken != null) {
            await _apiService.setRefreshToken(newRefreshToken);
          }
          return true;
        }
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('[AuthInterceptor] Token refresh failed: $e');
      }
      return false;
    }
  }

  /// Retry a failed request with new token
  Future<Response> _retryRequest(RequestOptions requestOptions) async {
    // Add new token to headers
    final token = await _apiService.getToken();
    if (token != null) {
      requestOptions.headers['Authorization'] = 'Bearer $token';
    }

    // Create a new Dio instance to avoid interceptor loops
    final dio = Dio();
    dio.options.baseUrl = AppConfig.apiBaseUrl;
    dio.options.headers.addAll(ApiConstants.defaultHeaders);

    return await dio.fetch(requestOptions);
  }

  /// Process any requests that were queued during token refresh
  Future<void> _processPendingRequests() async {
    final token = await _apiService.getToken();

    for (final request in _pendingRequests) {
      try {
        if (token != null) {
          request.headers['Authorization'] = 'Bearer $token';
        }

        // Retry the request
        final dio = Dio();
        dio.options.baseUrl = AppConfig.apiBaseUrl;
        dio.options.headers.addAll(ApiConstants.defaultHeaders);
        await dio.fetch(request);
      } catch (e) {
        if (kDebugMode) {
          print('[AuthInterceptor] Failed to process pending request: $e');
        }
      }
    }
  }

  /// Handle authentication failure by clearing tokens
  Future<void> _handleAuthFailure() async {
    try {
      await _apiService.clearToken();

      // Optionally navigate to login screen
      // This could be handled by a global auth controller
      if (kDebugMode) {
        print('[AuthInterceptor] Authentication failed, tokens cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[AuthInterceptor] Error handling auth failure: $e');
      }
    }
  }
}

/// Logging interceptor with enhanced formatting
class EnhancedLogInterceptor extends Interceptor {
  final bool logRequest;
  final bool logResponse;
  final bool logError;

  EnhancedLogInterceptor({
    this.logRequest = true,
    this.logResponse = true,
    this.logError = true,
  });

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (logRequest && kDebugMode) {
      print('┌─────────────────────────────────────────────────────────────');
      print('│ 🚀 REQUEST');
      print('│ ${options.method} ${options.uri}');
      print('│ Headers: ${_formatHeaders(options.headers)}');
      if (options.data != null) {
        print('│ Body: ${_formatData(options.data)}');
      }
      print('└─────────────────────────────────────────────────────────────');
    }
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (logResponse && kDebugMode) {
      print('┌─────────────────────────────────────────────────────────────');
      print('│ ✅ RESPONSE');
      print(
        '│ ${response.requestOptions.method} ${response.requestOptions.uri}',
      );
      print('│ Status: ${response.statusCode}');
      print('│ Data: ${_formatData(response.data)}');
      print('└─────────────────────────────────────────────────────────────');
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (logError && kDebugMode) {
      print('┌─────────────────────────────────────────────────────────────');
      print('│ ❌ ERROR');
      print('│ ${err.requestOptions.method} ${err.requestOptions.uri}');
      print('│ Status: ${err.response?.statusCode}');
      print('│ Message: ${err.message}');
      if (err.response?.data != null) {
        print('│ Error Data: ${_formatData(err.response!.data)}');
      }
      print('└─────────────────────────────────────────────────────────────');
    }
    handler.next(err);
  }

  String _formatHeaders(Map<String, dynamic> headers) {
    final filteredHeaders = Map<String, dynamic>.from(headers);
    // Hide sensitive headers
    if (filteredHeaders.containsKey('Authorization')) {
      filteredHeaders['Authorization'] = '***';
    }
    return filteredHeaders.toString();
  }

  String _formatData(dynamic data) {
    if (data == null) return 'null';
    if (data is String && data.length > 200) {
      return '${data.substring(0, 200)}...';
    }
    return data.toString();
  }
}

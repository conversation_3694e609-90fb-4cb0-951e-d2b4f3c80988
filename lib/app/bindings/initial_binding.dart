import 'package:get/get.dart';

import '../../services/api_service.dart';
import '../services/auth_api_service.dart';
import '../services/course_api_service.dart';
import '../services/topic_api_service.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Initialize base API service first
    Get.put<ApiService>(ApiService(), permanent: true);

    // Initialize feature-specific API services
    Get.put<AuthApiService>(AuthApiService(), permanent: true);
    Get.put<TopicApiService>(TopicApiService(), permanent: true);
    Get.put<CourseApiService>(CourseApiService(), permanent: true);
  }
}

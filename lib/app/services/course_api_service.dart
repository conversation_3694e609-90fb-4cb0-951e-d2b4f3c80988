import 'package:get/get.dart';

import '../constants/api_constants.dart';
import '../models/api_response.dart';
import '../../services/api_service.dart';

/// Course API service that handles all course-related endpoints
class CourseApiService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  /// Add a new course
  /// POST /courses/add
  Future<ApiResponse<Map<String, dynamic>>> addCourse({
    required String title,
    required String description,
    String? instructor,
    String? category,
    double? price,
    int? duration, // in minutes
    String? difficulty, // 'beginner', 'intermediate', 'advanced'
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) async {
    final requestData = {
      'title': title,
      'description': description,
      if (instructor != null) 'instructor': instructor,
      if (category != null) 'category': category,
      if (price != null) 'price': price,
      if (duration != null) 'duration': duration,
      if (difficulty != null) 'difficulty': difficulty,
      if (tags != null) 'tags': tags,
      if (metadata != null) 'metadata': metadata,
    };

    return await _apiService.safePost<Map<String, dynamic>>(
      ApiConstants.addCourse,
      data: requestData,
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get all courses with optional filtering
  /// GET /courses/getAll
  Future<ApiResponse<PaginatedResponse<Map<String, dynamic>>>> getAllCourses({
    int page = 1,
    int limit = 10,
    String? category,
    String? difficulty,
    String? instructor,
    double? minPrice,
    double? maxPrice,
    String? search,
    String? sortBy, // 'title', 'price', 'rating', 'created_at'
    String? sortOrder, // 'asc', 'desc'
  }) async {
    final queryParameters = <String, dynamic>{
      'page': page,
      'limit': limit,
      if (category != null) 'category': category,
      if (difficulty != null) 'difficulty': difficulty,
      if (instructor != null) 'instructor': instructor,
      if (minPrice != null) 'minPrice': minPrice,
      if (maxPrice != null) 'maxPrice': maxPrice,
      if (search != null) 'search': search,
      if (sortBy != null) 'sortBy': sortBy,
      if (sortOrder != null) 'sortOrder': sortOrder,
    };

    return await _apiService.safeGet<PaginatedResponse<Map<String, dynamic>>>(
      ApiConstants.getAllCourses,
      queryParameters: queryParameters,
      fromJson: (data) => PaginatedResponse.fromJson(
        data as Map<String, dynamic>,
        (item) => item as Map<String, dynamic>,
      ),
    );
  }

  /// Get a specific course by ID
  /// GET /courses/{courseId}
  Future<ApiResponse<Map<String, dynamic>>> getCourseById(String courseId) async {
    return await _apiService.safeGet<Map<String, dynamic>>(
      '${ApiConstants.coursesBase}/$courseId',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Update a course
  /// PATCH /courses/{courseId}
  Future<ApiResponse<Map<String, dynamic>>> updateCourse({
    required String courseId,
    String? title,
    String? description,
    String? instructor,
    String? category,
    double? price,
    int? duration,
    String? difficulty,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) async {
    final requestData = <String, dynamic>{};
    
    if (title != null) requestData['title'] = title;
    if (description != null) requestData['description'] = description;
    if (instructor != null) requestData['instructor'] = instructor;
    if (category != null) requestData['category'] = category;
    if (price != null) requestData['price'] = price;
    if (duration != null) requestData['duration'] = duration;
    if (difficulty != null) requestData['difficulty'] = difficulty;
    if (tags != null) requestData['tags'] = tags;
    if (metadata != null) requestData['metadata'] = metadata;

    // Note: Using the dynamic endpoint method from ApiConstants
    return await _apiService.safePut<Map<String, dynamic>>(
      ApiConstants.updateCourse(courseId),
      data: requestData,
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Delete a course
  /// DELETE /courses/{courseId}
  Future<ApiResponse<Map<String, dynamic>>> deleteCourse(String courseId) async {
    return await _apiService.safeDelete<Map<String, dynamic>>(
      ApiConstants.deleteCourse(courseId),
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get courses by category
  /// GET /courses/category/{category}
  Future<ApiResponse<List<Map<String, dynamic>>>> getCoursesByCategory({
    required String category,
    int page = 1,
    int limit = 10,
  }) async {
    final queryParameters = {
      'page': page,
      'limit': limit,
    };

    return await _apiService.safeGet<List<Map<String, dynamic>>>(
      '${ApiConstants.coursesBase}/category/$category',
      queryParameters: queryParameters,
      fromJson: (data) => (data as List).map((item) => item as Map<String, dynamic>).toList(),
    );
  }

  /// Get courses by instructor
  /// GET /courses/instructor/{instructorId}
  Future<ApiResponse<List<Map<String, dynamic>>>> getCoursesByInstructor({
    required String instructorId,
    int page = 1,
    int limit = 10,
  }) async {
    final queryParameters = {
      'page': page,
      'limit': limit,
    };

    return await _apiService.safeGet<List<Map<String, dynamic>>>(
      '${ApiConstants.coursesBase}/instructor/$instructorId',
      queryParameters: queryParameters,
      fromJson: (data) => (data as List).map((item) => item as Map<String, dynamic>).toList(),
    );
  }

  /// Search courses
  /// GET /courses/search
  Future<ApiResponse<PaginatedResponse<Map<String, dynamic>>>> searchCourses({
    required String query,
    int page = 1,
    int limit = 10,
    String? category,
    String? difficulty,
    double? minPrice,
    double? maxPrice,
  }) async {
    final queryParameters = <String, dynamic>{
      'q': query,
      'page': page,
      'limit': limit,
      if (category != null) 'category': category,
      if (difficulty != null) 'difficulty': difficulty,
      if (minPrice != null) 'minPrice': minPrice,
      if (maxPrice != null) 'maxPrice': maxPrice,
    };

    return await _apiService.safeGet<PaginatedResponse<Map<String, dynamic>>>(
      '${ApiConstants.coursesBase}/search',
      queryParameters: queryParameters,
      fromJson: (data) => PaginatedResponse.fromJson(
        data as Map<String, dynamic>,
        (item) => item as Map<String, dynamic>,
      ),
    );
  }

  /// Get popular courses
  /// GET /courses/popular
  Future<ApiResponse<List<Map<String, dynamic>>>> getPopularCourses({
    int limit = 10,
    String? timeframe, // 'week', 'month', 'year', 'all'
    String? category,
  }) async {
    final queryParameters = <String, dynamic>{
      'limit': limit,
      if (timeframe != null) 'timeframe': timeframe,
      if (category != null) 'category': category,
    };

    return await _apiService.safeGet<List<Map<String, dynamic>>>(
      '${ApiConstants.coursesBase}/popular',
      queryParameters: queryParameters,
      fromJson: (data) => (data as List).map((item) => item as Map<String, dynamic>).toList(),
    );
  }

  /// Get featured courses
  /// GET /courses/featured
  Future<ApiResponse<List<Map<String, dynamic>>>> getFeaturedCourses({
    int limit = 10,
  }) async {
    final queryParameters = {
      'limit': limit,
    };

    return await _apiService.safeGet<List<Map<String, dynamic>>>(
      '${ApiConstants.coursesBase}/featured',
      queryParameters: queryParameters,
      fromJson: (data) => (data as List).map((item) => item as Map<String, dynamic>).toList(),
    );
  }

  /// Enroll in a course
  /// POST /courses/{courseId}/enroll
  Future<ApiResponse<Map<String, dynamic>>> enrollInCourse(String courseId) async {
    return await _apiService.safePost<Map<String, dynamic>>(
      '${ApiConstants.coursesBase}/$courseId/enroll',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get user's enrolled courses
  /// GET /courses/enrolled
  Future<ApiResponse<List<Map<String, dynamic>>>> getEnrolledCourses({
    int page = 1,
    int limit = 10,
    String? status, // 'active', 'completed', 'paused'
  }) async {
    final queryParameters = <String, dynamic>{
      'page': page,
      'limit': limit,
      if (status != null) 'status': status,
    };

    return await _apiService.safeGet<List<Map<String, dynamic>>>(
      '${ApiConstants.coursesBase}/enrolled',
      queryParameters: queryParameters,
      fromJson: (data) => (data as List).map((item) => item as Map<String, dynamic>).toList(),
    );
  }

  /// Get course progress
  /// GET /courses/{courseId}/progress
  Future<ApiResponse<Map<String, dynamic>>> getCourseProgress(String courseId) async {
    return await _apiService.safeGet<Map<String, dynamic>>(
      '${ApiConstants.coursesBase}/$courseId/progress',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Update course progress
  /// POST /courses/{courseId}/progress
  Future<ApiResponse<Map<String, dynamic>>> updateCourseProgress({
    required String courseId,
    required double progressPercentage,
    String? lastCompletedLesson,
    Map<String, dynamic>? additionalData,
  }) async {
    final requestData = {
      'progressPercentage': progressPercentage,
      if (lastCompletedLesson != null) 'lastCompletedLesson': lastCompletedLesson,
      if (additionalData != null) ...additionalData,
    };

    return await _apiService.safePost<Map<String, dynamic>>(
      '${ApiConstants.coursesBase}/$courseId/progress',
      data: requestData,
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }
}

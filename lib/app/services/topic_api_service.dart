import 'package:get/get.dart';

import '../constants/api_constants.dart';
import '../models/api_response.dart';
import '../../services/api_service.dart';

/// Topic API service that handles all topic-related endpoints
class TopicApiService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  /// Add a new topic
  /// POST /topics/add
  Future<ApiResponse<Map<String, dynamic>>> addTopic({
    required String title,
    required String description,
    String? category,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) async {
    final requestData = {
      'title': title,
      'description': description,
      if (category != null) 'category': category,
      if (tags != null) 'tags': tags,
      if (metadata != null) 'metadata': metadata,
    };

    return await _apiService.safePost<Map<String, dynamic>>(
      ApiConstants.addTopic,
      data: requestData,
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get all topics with optional filtering
  /// GET /topics
  Future<ApiResponse<PaginatedResponse<Map<String, dynamic>>>> getTopics({
    int page = 1,
    int limit = 10,
    String? category,
    String? search,
    List<String>? tags,
  }) async {
    final queryParameters = <String, dynamic>{
      'page': page,
      'limit': limit,
      if (category != null) 'category': category,
      if (search != null) 'search': search,
      if (tags != null) 'tags': tags.join(','),
    };

    return await _apiService.safeGet<PaginatedResponse<Map<String, dynamic>>>(
      '${ApiConstants.topicsBase}',
      queryParameters: queryParameters,
      fromJson: (data) => PaginatedResponse.fromJson(
        data as Map<String, dynamic>,
        (item) => item as Map<String, dynamic>,
      ),
    );
  }

  /// Get a specific topic by ID
  /// GET /topics/{topicId}
  Future<ApiResponse<Map<String, dynamic>>> getTopicById(String topicId) async {
    return await _apiService.safeGet<Map<String, dynamic>>(
      '${ApiConstants.topicsBase}/$topicId',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Update a topic
  /// PUT /topics/{topicId}
  Future<ApiResponse<Map<String, dynamic>>> updateTopic({
    required String topicId,
    String? title,
    String? description,
    String? category,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) async {
    final requestData = <String, dynamic>{};
    
    if (title != null) requestData['title'] = title;
    if (description != null) requestData['description'] = description;
    if (category != null) requestData['category'] = category;
    if (tags != null) requestData['tags'] = tags;
    if (metadata != null) requestData['metadata'] = metadata;

    return await _apiService.safePut<Map<String, dynamic>>(
      '${ApiConstants.topicsBase}/$topicId',
      data: requestData,
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Delete a topic
  /// DELETE /topics/{topicId}
  Future<ApiResponse<Map<String, dynamic>>> deleteTopic(String topicId) async {
    return await _apiService.safeDelete<Map<String, dynamic>>(
      '${ApiConstants.topicsBase}/$topicId',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get topics by category
  /// GET /topics/category/{category}
  Future<ApiResponse<List<Map<String, dynamic>>>> getTopicsByCategory({
    required String category,
    int page = 1,
    int limit = 10,
  }) async {
    final queryParameters = {
      'page': page,
      'limit': limit,
    };

    return await _apiService.safeGet<List<Map<String, dynamic>>>(
      '${ApiConstants.topicsBase}/category/$category',
      queryParameters: queryParameters,
      fromJson: (data) => (data as List).map((item) => item as Map<String, dynamic>).toList(),
    );
  }

  /// Search topics
  /// GET /topics/search
  Future<ApiResponse<PaginatedResponse<Map<String, dynamic>>>> searchTopics({
    required String query,
    int page = 1,
    int limit = 10,
    String? category,
    List<String>? tags,
  }) async {
    final queryParameters = <String, dynamic>{
      'q': query,
      'page': page,
      'limit': limit,
      if (category != null) 'category': category,
      if (tags != null) 'tags': tags.join(','),
    };

    return await _apiService.safeGet<PaginatedResponse<Map<String, dynamic>>>(
      '${ApiConstants.topicsBase}/search',
      queryParameters: queryParameters,
      fromJson: (data) => PaginatedResponse.fromJson(
        data as Map<String, dynamic>,
        (item) => item as Map<String, dynamic>,
      ),
    );
  }

  /// Get popular topics
  /// GET /topics/popular
  Future<ApiResponse<List<Map<String, dynamic>>>> getPopularTopics({
    int limit = 10,
    String? timeframe, // 'week', 'month', 'year', 'all'
  }) async {
    final queryParameters = <String, dynamic>{
      'limit': limit,
      if (timeframe != null) 'timeframe': timeframe,
    };

    return await _apiService.safeGet<List<Map<String, dynamic>>>(
      '${ApiConstants.topicsBase}/popular',
      queryParameters: queryParameters,
      fromJson: (data) => (data as List).map((item) => item as Map<String, dynamic>).toList(),
    );
  }

  /// Get topic statistics
  /// GET /topics/{topicId}/stats
  Future<ApiResponse<Map<String, dynamic>>> getTopicStats(String topicId) async {
    return await _apiService.safeGet<Map<String, dynamic>>(
      '${ApiConstants.topicsBase}/$topicId/stats',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Like/Unlike a topic
  /// POST /topics/{topicId}/like
  Future<ApiResponse<Map<String, dynamic>>> toggleTopicLike(String topicId) async {
    return await _apiService.safePost<Map<String, dynamic>>(
      '${ApiConstants.topicsBase}/$topicId/like',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get user's favorite topics
  /// GET /topics/favorites
  Future<ApiResponse<List<Map<String, dynamic>>>> getFavoriteTopics({
    int page = 1,
    int limit = 10,
  }) async {
    final queryParameters = {
      'page': page,
      'limit': limit,
    };

    return await _apiService.safeGet<List<Map<String, dynamic>>>(
      '${ApiConstants.topicsBase}/favorites',
      queryParameters: queryParameters,
      fromJson: (data) => (data as List).map((item) => item as Map<String, dynamic>).toList(),
    );
  }
}

import 'package:get/get.dart';

import '../constants/api_constants.dart';
import '../models/api_response.dart';
import '../../services/api_service.dart';

/// Authentication API service that handles all auth-related endpoints
class AuthApiService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  /// Validate OTP for email verification
  /// POST /auth/validate-otp
  Future<ApiResponse<Map<String, dynamic>>> validateOtp({
    required String email,
    required String otp,
  }) async {
    return await _apiService.safePost<Map<String, dynamic>>(
      ApiConstants.validateOtp,
      data: {
        'email': email,
        'otp': otp,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Update user password
  /// POST /auth/update-password
  Future<ApiResponse<Map<String, dynamic>>> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    return await _apiService.safePost<Map<String, dynamic>>(
      ApiConstants.updatePassword,
      data: {
        'currentPassword': currentPassword,
        'newPassword': newPassword,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// User signup
  /// POST /auth/signup
  Future<ApiResponse<Map<String, dynamic>>> signup({
    required String name,
    required String email,
    required String password,
    Map<String, dynamic>? additionalData,
  }) async {
    final requestData = {
      'name': name,
      'email': email,
      'password': password,
      ...?additionalData,
    };

    return await _apiService.safePost<Map<String, dynamic>>(
      ApiConstants.signup,
      data: requestData,
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// User signin
  /// POST /auth/signin
  Future<ApiResponse<Map<String, dynamic>>> signin({
    required String email,
    required String password,
  }) async {
    final response = await _apiService.safePost<Map<String, dynamic>>(
      ApiConstants.signin,
      data: {
        'email': email,
        'password': password,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );

    // If signin is successful, store the token
    if (response.success && response.data != null) {
      final token = response.data!['token'] as String?;
      final refreshToken = response.data!['refreshToken'] as String?;
      
      if (token != null) {
        await _apiService.setToken(token);
      }
      if (refreshToken != null) {
        await _apiService.setRefreshToken(refreshToken);
      }
    }

    return response;
  }

  /// Set user password (for first-time setup)
  /// POST /auth/set-password
  Future<ApiResponse<Map<String, dynamic>>> setPassword({
    required String email,
    required String password,
    required String otp,
  }) async {
    return await _apiService.safePost<Map<String, dynamic>>(
      ApiConstants.setPassword,
      data: {
        'email': email,
        'password': password,
        'otp': otp,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Reset user password
  /// POST /auth/reset-password
  Future<ApiResponse<Map<String, dynamic>>> resetPassword({
    required String email,
  }) async {
    return await _apiService.safePost<Map<String, dynamic>>(
      ApiConstants.resetPassword,
      data: {
        'email': email,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Send email verification
  /// POST /auth/email-verification
  Future<ApiResponse<Map<String, dynamic>>> sendEmailVerification({
    required String email,
  }) async {
    return await _apiService.safePost<Map<String, dynamic>>(
      ApiConstants.emailVerification,
      data: {
        'email': email,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// User signout
  /// GET /auth/signout
  Future<ApiResponse<Map<String, dynamic>>> signout() async {
    final response = await _apiService.safeGet<Map<String, dynamic>>(
      ApiConstants.signout,
      fromJson: (data) => data as Map<String, dynamic>,
    );

    // Clear stored tokens regardless of response
    await _apiService.clearToken();

    return response;
  }

  /// Check if user is authenticated (has valid token)
  Future<bool> isAuthenticated() async {
    final token = await _apiService.getToken();
    return token != null && token.isNotEmpty;
  }

  /// Get current user token
  Future<String?> getCurrentToken() async {
    return await _apiService.getToken();
  }

  /// Refresh authentication token
  Future<ApiResponse<Map<String, dynamic>>> refreshToken() async {
    final refreshToken = await _apiService.getRefreshToken();
    if (refreshToken == null) {
      return ApiResponse.error(
        error: const ApiError(
          code: 'NO_REFRESH_TOKEN',
          message: 'No refresh token available',
        ),
      );
    }

    final response = await _apiService.safePost<Map<String, dynamic>>(
      '/auth/refresh-token', // This endpoint might need to be added to ApiConstants
      data: {
        'refreshToken': refreshToken,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );

    // If refresh is successful, store the new tokens
    if (response.success && response.data != null) {
      final newToken = response.data!['token'] as String?;
      final newRefreshToken = response.data!['refreshToken'] as String?;
      
      if (newToken != null) {
        await _apiService.setToken(newToken);
      }
      if (newRefreshToken != null) {
        await _apiService.setRefreshToken(newRefreshToken);
      }
    }

    return response;
  }

  /// Clear all authentication data
  Future<void> clearAuthData() async {
    await _apiService.clearToken();
  }
}

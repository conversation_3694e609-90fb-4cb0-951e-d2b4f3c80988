import 'package:dio/dio.dart';
import '../constants/api_constants.dart';
import '../models/api_response.dart';

/// Base class for all API exceptions
abstract class ApiException implements Exception {
  final String message;
  final String? code;
  final int? statusCode;
  final dynamic originalError;

  const ApiException({
    required this.message,
    this.code,
    this.statusCode,
    this.originalError,
  });

  @override
  String toString() {
    return 'ApiException(message: $message, code: $code, statusCode: $statusCode)';
  }
}

/// Network-related exceptions (no internet, timeout, etc.)
class NetworkException extends ApiException {
  const NetworkException({
    required super.message,
    super.code,
    super.originalError,
  });

  factory NetworkException.noInternet() {
    return const NetworkException(
      message: ApiConstants.networkError,
      code: 'NO_INTERNET',
    );
  }

  factory NetworkException.timeout() {
    return const NetworkException(
      message: ApiConstants.timeoutError,
      code: 'TIMEOUT',
    );
  }
}

/// Server error exceptions (5xx status codes)
class ServerException extends ApiException {
  const ServerException({
    required super.message,
    super.code,
    super.statusCode,
    super.originalError,
  });

  factory ServerException.internal() {
    return const ServerException(
      message: ApiConstants.serverError,
      code: 'INTERNAL_SERVER_ERROR',
      statusCode: ApiConstants.statusInternalServerError,
    );
  }
}

/// Client error exceptions (4xx status codes)
class ClientException extends ApiException {
  const ClientException({
    required super.message,
    super.code,
    super.statusCode,
    super.originalError,
  });

  factory ClientException.unauthorized() {
    return const ClientException(
      message: ApiConstants.unauthorizedError,
      code: 'UNAUTHORIZED',
      statusCode: ApiConstants.statusUnauthorized,
    );
  }

  factory ClientException.forbidden() {
    return const ClientException(
      message: 'Access forbidden',
      code: 'FORBIDDEN',
      statusCode: ApiConstants.statusForbidden,
    );
  }

  factory ClientException.notFound() {
    return const ClientException(
      message: 'Resource not found',
      code: 'NOT_FOUND',
      statusCode: ApiConstants.statusNotFound,
    );
  }

  factory ClientException.badRequest(String message) {
    return ClientException(
      message: message,
      code: 'BAD_REQUEST',
      statusCode: ApiConstants.statusBadRequest,
    );
  }
}

/// Validation error exception for form validation failures
class ValidationException extends ApiException {
  final List<ValidationError> validationErrors;

  const ValidationException({
    required super.message,
    required this.validationErrors,
    super.code = 'VALIDATION_ERROR',
    super.statusCode = ApiConstants.statusBadRequest,
  });

  /// Get validation error for a specific field
  ValidationError? getFieldError(String field) {
    try {
      return validationErrors.firstWhere((error) => error.field == field);
    } catch (e) {
      return null;
    }
  }

  /// Check if a specific field has validation error
  bool hasFieldError(String field) {
    return validationErrors.any((error) => error.field == field);
  }
}

/// Authentication-related exceptions
class AuthException extends ApiException {
  const AuthException({
    required super.message,
    super.code,
    super.statusCode = ApiConstants.statusUnauthorized,
    super.originalError,
  });

  factory AuthException.tokenExpired() {
    return const AuthException(
      message: 'Authentication token has expired',
      code: 'TOKEN_EXPIRED',
    );
  }

  factory AuthException.invalidCredentials() {
    return const AuthException(
      message: 'Invalid email or password',
      code: 'INVALID_CREDENTIALS',
    );
  }

  factory AuthException.accountLocked() {
    return const AuthException(
      message: 'Account has been locked',
      code: 'ACCOUNT_LOCKED',
    );
  }
}

/// Unknown or unexpected exceptions
class UnknownException extends ApiException {
  const UnknownException({
    super.message = ApiConstants.unknownError,
    super.code = 'UNKNOWN_ERROR',
    super.originalError,
  });
}

/// Utility class to convert Dio errors to custom API exceptions
class ApiExceptionHandler {
  /// Convert DioException to appropriate ApiException
  static ApiException handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkException.timeout();

      case DioExceptionType.connectionError:
        return NetworkException.noInternet();

      case DioExceptionType.badResponse:
        return _handleResponseError(error);

      case DioExceptionType.cancel:
        return const UnknownException(
          message: 'Request was cancelled',
          code: 'REQUEST_CANCELLED',
        );

      case DioExceptionType.unknown:
        return UnknownException(originalError: error);

      default:
        return UnknownException(originalError: error);
    }
  }

  /// Handle HTTP response errors (4xx, 5xx)
  static ApiException _handleResponseError(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    // Try to parse error from response
    if (responseData is Map<String, dynamic>) {
      final apiError = ApiError.fromJson(responseData);
      
      // Handle validation errors
      if (apiError.validationErrors != null && apiError.validationErrors!.isNotEmpty) {
        return ValidationException(
          message: apiError.message,
          validationErrors: apiError.validationErrors!,
        );
      }

      // Handle other errors based on status code
      switch (statusCode) {
        case ApiConstants.statusUnauthorized:
          return AuthException(
            message: apiError.message,
            code: apiError.code,
          );
        case ApiConstants.statusForbidden:
          return ClientException.forbidden();
        case ApiConstants.statusNotFound:
          return ClientException.notFound();
        case ApiConstants.statusBadRequest:
          return ClientException.badRequest(apiError.message);
        default:
          if (statusCode != null && statusCode >= 500) {
            return ServerException(
              message: apiError.message,
              code: apiError.code,
              statusCode: statusCode,
            );
          }
          return ClientException(
            message: apiError.message,
            code: apiError.code,
            statusCode: statusCode,
          );
      }
    }

    // Fallback error handling
    switch (statusCode) {
      case ApiConstants.statusUnauthorized:
        return ClientException.unauthorized();
      case ApiConstants.statusForbidden:
        return ClientException.forbidden();
      case ApiConstants.statusNotFound:
        return ClientException.notFound();
      default:
        if (statusCode != null && statusCode >= 500) {
          return ServerException.internal();
        }
        return ClientException.badRequest('Request failed');
    }
  }

  /// Convert any exception to ApiException
  static ApiException handleGenericError(dynamic error) {
    if (error is ApiException) {
      return error;
    }
    if (error is DioException) {
      return handleDioError(error);
    }
    return UnknownException(originalError: error);
  }
}

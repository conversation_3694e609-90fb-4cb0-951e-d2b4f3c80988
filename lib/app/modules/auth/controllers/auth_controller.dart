import 'package:get/get.dart';

import '../models/user_model.dart';
import '../services/auth_service.dart';

class AuthController extends GetxController {
  final AuthService _authService = Get.find();

  Rxn<User> user = Rxn<User>();
  RxBool loading = false.obs;
  RxBool googleLoading = false.obs;
  RxBool facebookLoading = false.obs;
  RxBool microsoftLoading = false.obs;

  @override
  void onInit() async {
    super.onInit();
    user.bindStream(_authService.user);
    final token = await _authService.getToken();
    if (token != null) {
      // Optionally fetch user profile with token
      user.value = User(id: '', name: '', email: '', token: token);
    }
  }

  Future<void> signInWithGoogle() async {
    googleLoading.value = true;
    try {
      await _authService.signInWithGoogle();
    } finally {
      googleLoading.value = false;
    }
  }

  Future<void> signInWithFacebook() async {
    facebookLoading.value = true;
    try {
      await _authService.signInWithFacebook();
    } finally {
      facebookLoading.value = false;
    }
  }

  Future<void> signInWithMicrosoft() async {
    microsoftLoading.value = true;
    try {
      await _authService.signInWithMicrosoft();
    } finally {
      microsoftLoading.value = false;
    }
  }

  Future<void> login(String email, String password) async {
    loading.value = true;
    try {
      user.value = await _authService.login(email, password);
    } finally {
      loading.value = false;
    }
  }

  Future<void> requestOtp(String email) async {
    loading.value = true;
    try {
      await _authService.requestOtp(email);
    } finally {
      loading.value = false;
    }
  }

  Future<bool> verifyOtp(String email, String otp) async {
    loading.value = true;
    try {
      return await _authService.verifyOtp(email, otp);
    } finally {
      loading.value = false;
    }
  }

  Future<void> registerWithPassword(
    String email,
    String otp,
    String password,
  ) async {
    loading.value = true;
    try {
      user.value = await _authService.registerWithPassword(
        email,
        otp,
        password,
      );
    } finally {
      loading.value = false;
    }
  }

  Future<User> register(String name, String email, String password) async {
    loading.value = true;
    try {
      user.value = await _authService.register(name, email, password);
      return user.value!;
    } finally {
      loading.value = false;
    }
  }

  Future<void> updateProfile({
    required String name,
    required String university,
    required String semester,
    required String branch,
  }) async {
    loading.value = true;
    try {
      user.value = await _authService.updateProfile(
        name: name,
        university: university,
        semester: semester,
        branch: branch,
      );
    } finally {
      loading.value = false;
    }
  }

  Future<void> logout() async {
    await _authService.logout();
    user.value = null;
  }
}

import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase;
import 'package:flutter/cupertino.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';

import '../models/user_model.dart';

class AuthService extends GetxService {
  final _firebaseAuth = firebase.FirebaseAuth.instance;
  final _googleSignIn = GoogleSignIn.instance;
  final _facebookSignIn = FacebookAuth.instance;
  final Dio _dio = Dio(BaseOptions(baseUrl: 'https://api.example.com'));
  final _storage = const FlutterSecureStorage();

  Future<User?> login(String email, String password) async {
    try {
      final response = await _dio.post(
        '/login',
        data: {'email': email, 'password': password},
      );
      final user = User.fromJson(response.data);
      await _storage.write(key: 'token', value: user.token);
      return user;
    } catch (e) {
      rethrow;
    }
  }

  Future<User?> register(String name, String email, String password) async {
    try {
      final response = await _dio.post(
        '/register',
        data: {'name': name, 'email': email, 'password': password},
      );
      final user = User.fromJson(response.data);
      await _storage.write(key: 'token', value: user.token);
      return user;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> requestOtp(String email) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      debugPrint('OTP requested for $email');
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> verifyOtp(String email, String otp) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      debugPrint('OTP verified for $email with otp $otp');
      return otp == '123456'; // Simulate successful OTP
    } catch (e) {
      rethrow;
    }
  }

  Future<User> registerWithPassword(
    String email,
    String otp,
    String password,
  ) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      return User(
        id: '123',
        name: 'John Doe',
        email: email,
        token: 'fake-token',
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<User> updateProfile({
    required String name,
    required String university,
    required String semester,
    required String branch,
  }) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      final user = User(
        id: '123',
        name: name,
        email: '<EMAIL>',
        token: 'fake-token',
      );
      await _storage.write(key: 'token', value: user.token);
      debugPrint('Profile updated for $name');
      return user;
    } catch (e) {
      rethrow;
    }
  }

  Stream<User?> get user =>
      _firebaseAuth.authStateChanges().map(_userFromFirebase);

  User? _userFromFirebase(firebase.User? user) {
    if (user == null) {
      return null;
    }
    return User(
      id: user.uid,
      email: user.email ?? '',
      name: user.displayName ?? '',
      token: '', // Token management will be handled by Firebase SDK
    );
  }

  Future<User?> signInWithGoogle() async {
    try {
      await _googleSignIn.initialize(
        serverClientId:
            "1066625703529-u3kst2euqi4m35stokhu0ms862t1cgdk.apps.googleusercontent.com",
      );
      final googleUser = await _googleSignIn.authenticate();
      final googleAuth = googleUser.authentication;
      final credential = firebase.GoogleAuthProvider.credential(
        idToken: googleAuth.idToken,
      );
      final userCredential = await _firebaseAuth.signInWithCredential(
        credential,
      );
      return _userFromFirebase(userCredential.user);
    } catch (e) {
      rethrow;
    }
  }

  Future<User?> signInWithFacebook() async {
    try {
      final result = await _facebookSignIn.login();
      if (result.status == LoginStatus.success) {
        final fbCredential = firebase.FacebookAuthProvider.credential(
          result.accessToken?.tokenString ?? '',
        );
        final userCredential = await _firebaseAuth.signInWithCredential(
          fbCredential,
        );
        return _userFromFirebase(userCredential.user);
      } else {
        return null;
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<User?> signInWithMicrosoft() async {
    final microsoftProvider = firebase.MicrosoftAuthProvider();
    final userCredential = await _firebaseAuth.signInWithProvider(
      microsoftProvider,
    );
    return _userFromFirebase(userCredential.user);
  }

  Future<void> logout() async {
    await _googleSignIn.signOut();
    await _firebaseAuth.signOut();
    await _facebookSignIn.logOut();
    await _storage.delete(key: 'token');
  }

  Future<String?> getToken() => _storage.read(key: 'token');
}

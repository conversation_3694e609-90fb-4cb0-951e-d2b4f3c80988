import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:somayya_academy/app/constants/app_constants.dart';
import 'package:somayya_academy/app/constants/string_constants.dart';
import 'package:somayya_academy/app/utils/validator.dart';
import 'package:somayya_academy/components/app_button.dart';
import 'package:somayya_academy/components/app_text_field.dart';
import 'package:somayya_academy/components/social_login_button.dart';

import '../controllers/auth_controller.dart';

enum LoginType { email, google, facebook, microsoft }

class LoginView extends StatelessWidget {
  LoginView({super.key});

  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    return Scaffold(
      appBar: AppBar(title: const Text(StringConstants.login)),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.padding),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            children: [
              AppTextField(
                controller: _emailController,
                labelText: StringConstants.email,
                keyboardType: TextInputType.emailAddress,
                validator: Validator.validateEmail,
              ),
              AppTextField(
                controller: _passwordController,
                labelText: StringConstants.password,
                obscureText: true,
                validator: Validator.validatePassword,
              ),
              const SizedBox(height: AppConstants.sizedBoxHeight),
              Obx(
                () => AppButton(
                  text: StringConstants.login,
                  isLoading: authController.loading.value,
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      authController.login(
                        _emailController.text,
                        _passwordController.text,
                      );
                    }
                  },
                ),
              ),
              const SizedBox(height: AppConstants.sizedBoxHeight),
              const Row(
                children: [
                  Expanded(child: Divider()),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text('OR CONTINUE WITH'),
                  ),
                  Expanded(child: Divider()),
                ],
              ),
              const SizedBox(height: AppConstants.sizedBoxHeight),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: 20,
                children: [
                  SocialLoginButton(
                    loginType: 'google',
                    onPressed: authController.signInWithGoogle,
                  ),
                  SocialLoginButton(
                    loginType: 'facebook',
                    onPressed: authController.signInWithFacebook,
                  ),
                  SocialLoginButton(
                    loginType: 'microsoft',
                    onPressed: authController.signInWithMicrosoft,
                  ),
                ],
              ),

              // TextButton(
              //   onPressed: () {
              //     context.push(RouteConstants.register);
              //   },
              //   child: const Text(StringConstants.noAccount),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart' hide Response;

import '../app/constants/api_constants.dart';
import '../app/exceptions/api_exceptions.dart';
import '../app/interceptors/auth_interceptor.dart';
import '../app/models/api_response.dart';
import '../config/app_config.dart';

class ApiService extends GetxService {
  late Dio _dio;
  static const _storage = FlutterSecureStorage();

  // Token management
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';

  @override
  void onInit() {
    super.onInit();
    _dio = Dio();
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.options.baseUrl = AppConfig.apiBaseUrl;
    _dio.options.connectTimeout = ApiConstants.connectTimeout;
    _dio.options.receiveTimeout = ApiConstants.receiveTimeout;
    _dio.options.sendTimeout = ApiConstants.sendTimeout;
    _dio.options.headers.addAll(ApiConstants.defaultHeaders);

    // Add enhanced logging interceptor for dev environment
    if (AppConfig.enableDetailedLogging) {
      _dio.interceptors.add(EnhancedLogInterceptor());
    }

    // Add authentication interceptor
    _dio.interceptors.add(AuthInterceptor());

    // Add flavor-specific headers interceptor
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add flavor-specific headers if needed
          if (AppConfig.isDev) {
            options.headers['X-Environment'] = 'development';
          }
          handler.next(options);
        },
      ),
    );
  }

  // Example API methods
  Future<Response> get(String path, {Map<String, dynamic>? queryParameters}) {
    return _dio.get(path, queryParameters: queryParameters);
  }

  Future<Response> post(String path, {dynamic data}) {
    return _dio.post(path, data: data);
  }

  Future<Response> put(String path, {dynamic data}) {
    return _dio.put(path, data: data);
  }

  Future<Response> delete(String path) {
    return _dio.delete(path);
  }

  // Example method showing flavor-specific behavior
  Future<Map<String, dynamic>> getAppInfo() async {
    try {
      final response = await get('/app/info');
      return response.data;
    } catch (e) {
      // In dev, return mock data if API fails
      if (AppConfig.isDev) {
        return {
          'version': '1.0.0-dev',
          'environment': 'development',
          'features': ['debug_mode', 'mock_data'],
        };
      }
      rethrow;
    }
  }

  // Token Management Methods
  Future<String?> getToken() async {
    try {
      return await _storage.read(key: _tokenKey);
    } catch (e) {
      if (kDebugMode) {
        print('[API] Error reading token: $e');
      }
      return null;
    }
  }

  Future<void> setToken(String token) async {
    try {
      await _storage.write(key: _tokenKey, value: token);
    } catch (e) {
      if (kDebugMode) {
        print('[API] Error saving token: $e');
      }
    }
  }

  Future<void> clearToken() async {
    try {
      await _storage.delete(key: _tokenKey);
      await _storage.delete(key: _refreshTokenKey);
    } catch (e) {
      if (kDebugMode) {
        print('[API] Error clearing tokens: $e');
      }
    }
  }

  Future<String?> getRefreshToken() async {
    try {
      return await _storage.read(key: _refreshTokenKey);
    } catch (e) {
      if (kDebugMode) {
        print('[API] Error reading refresh token: $e');
      }
      return null;
    }
  }

  Future<void> setRefreshToken(String refreshToken) async {
    try {
      await _storage.write(key: _refreshTokenKey, value: refreshToken);
    } catch (e) {
      if (kDebugMode) {
        print('[API] Error saving refresh token: $e');
      }
    }
  }

  // Enhanced HTTP Methods with Error Handling
  Future<ApiResponse<T>> safeGet<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await get(path, queryParameters: queryParameters);
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> safePost<T>(
    String path, {
    dynamic data,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await post(path, data: data);
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> safePut<T>(
    String path, {
    dynamic data,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await put(path, data: data);
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> safeDelete<T>(
    String path, {
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await delete(path);
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  // Response and Error Handling
  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? fromJson,
  ) {
    if (response.statusCode == 200 || response.statusCode == 201) {
      final data = response.data;
      if (data is Map<String, dynamic>) {
        return ApiResponse.fromJson(data, fromJson);
      } else {
        return ApiResponse.success(
          data: fromJson != null ? fromJson(data) : data as T?,
        );
      }
    } else {
      return ApiResponse.error(
        error: ApiError(
          code: 'HTTP_${response.statusCode}',
          message: 'Request failed with status ${response.statusCode}',
        ),
      );
    }
  }

  ApiResponse<T> _handleError<T>(dynamic error) {
    final apiException = ApiExceptionHandler.handleGenericError(error);
    return ApiResponse.error(
      error: ApiError(
        code: apiException.code ?? 'UNKNOWN_ERROR',
        message: apiException.message,
      ),
    );
  }
}
